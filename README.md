# ATO Wordle - Australian Taxation Office Word Game

A fun, educational word game based on Wordle, featuring Australian Taxation Office (ATO) terminology and concepts.

## Features

### Core Gameplay
- **Classic Wordle mechanics**: Guess a 5-letter word in 6 attempts
- **Color-coded feedback**: 
  - 🟩 Green: Correct letter in correct position
  - 🟨 Yellow: Correct letter in wrong position  
  - ⬜ Gray: Letter not in the word
- **Two game modes**:
  - **ATO Mode**: Uses ATO-specific terminology (TAXES, AUDIT, LODGE, etc.)
  - **Normal Mode**: Uses common English words

### Interactive Features
- **On-screen keyboard**: Click letters to make guesses
- **Physical keyboard support**: Type directly on your keyboard
- **Statistics tracking**: Win percentage, streaks, and guess distribution
- **Local storage**: Your progress is saved in your browser

### ATO-Themed Content
- **80+ ATO-related words**: Including TAXES, AUDIT, SUPER, REBATE, LODGE, INCOME, etc.
- **Educational facts**: "Did You Know?" boxes with ATO trivia when you win
- **Professional styling**: Clean, office-appropriate design with ATO color scheme

## How to Play

1. **Start the game**: A random 5-letter word is selected
2. **Make your guess**: Type or click letters to form a 5-letter word
3. **Submit**: Press ENTER or click the ENTER button
4. **Learn from feedback**: Use the color clues to refine your next guess
5. **Win or learn**: Solve it in 6 tries or see the answer!

## Technical Details

### Files
- `index.html` - Main game interface
- `style.css` - Styling and responsive design
- `script.js` - Game logic and functionality

### Browser Compatibility
- Works in all modern browsers (Chrome, Firefox, Safari, Edge)
- Responsive design for desktop and mobile devices
- No external dependencies or API calls required

### Privacy & Security
- **Fully offline**: No network requests after initial load
- **No tracking**: No analytics or data collection
- **Local storage only**: Statistics saved in your browser only
- **Work-safe**: Appropriate for office environments

## Installation & Setup

1. **Download the files**: Save all three files to a folder
2. **Open in browser**: Double-click `index.html` or serve via web server
3. **Start playing**: No installation or setup required!

### For Development/Testing
```bash
# Serve locally (optional)
python -m http.server 8000
# Then visit http://localhost:8000
```

## Game Controls

### Keyboard Shortcuts
- **A-Z**: Enter letters
- **Enter**: Submit guess
- **Backspace**: Delete last letter

### Buttons
- **ATO Mode/Normal Mode**: Switch between word sets
- **New Game**: Start fresh game
- **Stats**: View your statistics
- **Close**: Close modals and fact boxes

## Word Lists

### ATO Mode Words (Examples)
- TAXES, AUDIT, LODGE, SUPER, REBATE
- DEBIT, CREDIT, TRUST, ENTITY, INCOME
- OFFSET, CLAIM, REFUND, PAYG, FRINGE
- And many more taxation-related terms!

### Normal Mode
- Common 5-letter English words
- Suitable for general word game enjoyment

## Educational Value

Perfect for:
- **ATO staff training**: Familiarize with common terminology
- **Tax education**: Learn key concepts through gameplay  
- **Office breaks**: Quick, engaging mental exercise
- **Team building**: Compare scores and streaks

## Browser Support

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers

## License

This is a demonstration project created for educational and entertainment purposes. The game mechanics are inspired by Wordle by Josh Wardle.

---

**Enjoy playing ATO Wordle and learning about Australian taxation terminology!** 🇦🇺📊
