/* ATO Wordle Styles */
:root {
    --ato-blue: #003366;
    --ato-light-blue: #0066cc;
    --ato-green: #006633;
    --ato-orange: #ff6600;
    --correct: #6aaa64;
    --present: #c9b458;
    --absent: #787c7e;
    --white: #ffffff;
    --black: #212529;
    --border: #d3d6da;
    --key-bg: #d3d6da;
    --key-bg-light: #f6f7f8;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--white);
    color: var(--black);
    line-height: 1.6;
}

.container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border);
    padding-bottom: 20px;
}

.title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--ato-blue);
    margin-bottom: 5px;
    letter-spacing: 2px;
}

.subtitle {
    color: var(--ato-light-blue);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    background-color: var(--ato-blue);
    color: var(--white);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.btn:hover {
    background-color: var(--ato-light-blue);
}

/* Game Board */
.game-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

.game-board {
    display: grid;
    grid-template-rows: repeat(6, 1fr);
    gap: 5px;
    margin-bottom: 20px;
}

.row {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 5px;
}

.tile {
    width: 62px;
    height: 62px;
    border: 2px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    text-transform: uppercase;
    background-color: var(--white);
    transition: all 0.3s ease;
}

.tile.filled {
    border-color: var(--absent);
    animation: pop 0.1s ease-in-out;
}

.tile.correct {
    background-color: var(--correct);
    border-color: var(--correct);
    color: var(--white);
}

.tile.present {
    background-color: var(--present);
    border-color: var(--present);
    color: var(--white);
}

.tile.absent {
    background-color: var(--absent);
    border-color: var(--absent);
    color: var(--white);
}

@keyframes pop {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Keyboard */
.keyboard {
    width: 100%;
    max-width: 500px;
}

.keyboard-row {
    display: flex;
    justify-content: center;
    gap: 6px;
    margin-bottom: 8px;
}

.key {
    min-width: 43px;
    height: 58px;
    border: none;
    border-radius: 4px;
    background-color: var(--key-bg);
    color: var(--black);
    font-size: 0.9rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.1s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.key:hover {
    background-color: var(--key-bg-light);
}

.key.key-large {
    min-width: 65px;
    font-size: 0.8rem;
}

.key.correct {
    background-color: var(--correct);
    color: var(--white);
}

.key.present {
    background-color: var(--present);
    color: var(--white);
}

.key.absent {
    background-color: var(--absent);
    color: var(--white);
}

/* Messages */
.message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--black);
    color: var(--white);
    padding: 16px 24px;
    border-radius: 4px;
    font-weight: bold;
    z-index: 1000;
    animation: fadeInOut 2s ease-in-out;
}

.message.hidden {
    display: none;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    20%, 80% { opacity: 1; }
}

/* Did You Know Box */
.did-you-know {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--white);
    border: 2px solid var(--ato-blue);
    border-radius: 8px;
    padding: 20px;
    max-width: 400px;
    z-index: 1001;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.did-you-know.hidden {
    display: none;
}

.did-you-know h3 {
    color: var(--ato-blue);
    margin-bottom: 10px;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--absent);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--ato-blue);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--absent);
}

/* Instructions */
.instructions {
    margin-top: 30px;
    padding: 20px;
    background-color: var(--key-bg-light);
    border-radius: 8px;
    text-align: center;
}

.instructions h3 {
    color: var(--ato-blue);
    margin-bottom: 10px;
}

.example-tiles {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin: 15px 0;
}

.example-tiles .tile {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
}

/* Distribution Chart */
.guess-distribution {
    margin-top: 20px;
}

.guess-distribution h3 {
    color: var(--ato-blue);
    margin-bottom: 15px;
    text-align: center;
}

/* Animations */
@keyframes flip {
    0% { transform: rotateX(0); }
    50% { transform: rotateX(-90deg); }
    100% { transform: rotateX(0); }
}

.tile.flip {
    animation: flip 0.6s ease-in-out;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .title {
        font-size: 2rem;
    }

    .tile {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .key {
        min-width: 35px;
        height: 50px;
        font-size: 0.8rem;
    }

    .key.key-large {
        min-width: 55px;
    }

    .modal-content {
        width: 95%;
        padding: 15px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}
