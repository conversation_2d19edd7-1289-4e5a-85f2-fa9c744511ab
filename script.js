// ATO Wordle Game
class ATOWordle {
    constructor() {
        // ATO-related word bank (5-letter words)
        this.atoWords = [
            'TAXES', 'AUDIT', 'LODGE', 'SUPER', 'REBATE', 'DEBIT', 'CREDIT', 'TRUST',
            'ENTITY', 'INCOME', 'OFFSET', '<PERSON><PERSON><PERSON>', 'REFUND', 'PAYG', 'FRINGE', 'BONUS',
            'ASSET', 'LEASE', 'TRADE', 'STOCK', 'SHARE', 'PROFIT', 'LOSS', 'GROSS',
            'NETT', 'TOTAL', 'BASIC', 'FINAL', 'ANNUAL', 'MONTH', 'DAILY', 'HOURS',
            'WAGES', 'SALARY', 'BONUS', 'AWARD', 'RATES', 'LEAVE', 'SICK', 'LONG',
            'SHORT', 'TERM', 'FIXED', 'FLOAT', 'INDEX', 'BENCH', 'PRIME', 'YIELD',
            'BOND', 'FUND', 'UNIT', 'CASH', 'BANK', 'LOAN', 'DEBT', 'EQUITY',
            'VALUE', 'PRICE', 'COST', 'SPEND', 'SAVE', 'INVEST', 'RISK', 'SAFE',
            'LEGAL', 'VALID', 'VOID', 'NULL', 'TRUE', 'FALSE', 'RIGHT', 'WRONG',
            'GOOD', 'BAD', 'HIGH', 'LOW', 'UP', 'DOWN', 'IN', 'OUT', 'ON', 'OFF'
        ];

        // Regular English words for normal mode
        this.englishWords = [
            'ABOUT', 'ABOVE', 'ABUSE', 'ACTOR', 'ACUTE', 'ADMIT', 'ADOPT', 'ADULT',
            'AFTER', 'AGAIN', 'AGENT', 'AGREE', 'AHEAD', 'ALARM', 'ALBUM', 'ALERT',
            'ALIEN', 'ALIGN', 'ALIKE', 'ALIVE', 'ALLOW', 'ALONE', 'ALONG', 'ALTER',
            'AMONG', 'ANGER', 'ANGLE', 'ANGRY', 'APART', 'APPLE', 'APPLY', 'ARENA',
            'ARGUE', 'ARISE', 'ARRAY', 'ASIDE', 'ASSET', 'AVOID', 'AWAKE', 'AWARD',
            'AWARE', 'BADLY', 'BAKER', 'BASES', 'BASIC', 'BEACH', 'BEGAN', 'BEGIN',
            'BEING', 'BELOW', 'BENCH', 'BILLY', 'BIRTH', 'BLACK', 'BLAME', 'BLANK',
            'BLIND', 'BLOCK', 'BLOOD', 'BOARD', 'BOOST', 'BOOTH', 'BOUND', 'BRAIN',
            'BRAND', 'BRASS', 'BRAVE', 'BREAD', 'BREAK', 'BREED', 'BRIEF', 'BRING',
            'BROAD', 'BROKE', 'BROWN', 'BUILD', 'BUILT', 'BUYER', 'CABLE', 'CALIF'
        ];

        // ATO fun facts
        this.atoFacts = [
            "The ATO was established in 1910 as the Commonwealth Taxation Office.",
            "Australia introduced GST (Goods and Services Tax) on 1 July 2000.",
            "The ATO processes over 15 million individual tax returns each year.",
            "ABN stands for Australian Business Number and was introduced in 2000.",
            "The ATO's headquarters is located in Canberra, ACT.",
            "PAYG (Pay As You Go) withholding was introduced in 2000.",
            "The ATO manages over $500 billion in revenue collection annually.",
            "Superannuation guarantee was introduced in 1992 at 3% of wages.",
            "The ATO has over 20,000 employees across Australia.",
            "Tax File Numbers (TFN) were introduced in 1988."
        ];

        this.gameState = {
            currentRow: 0,
            currentCol: 0,
            gameOver: false,
            won: false,
            targetWord: '',
            guesses: [],
            atoMode: true
        };

        this.init();
    }

    init() {
        this.createGameBoard();
        this.setupEventListeners();
        this.loadGameState();
        this.newGame();
    }

    createGameBoard() {
        const gameBoard = document.getElementById('game-board');
        gameBoard.innerHTML = '';

        for (let i = 0; i < 6; i++) {
            const row = document.createElement('div');
            row.className = 'row';
            row.id = `row-${i}`;

            for (let j = 0; j < 5; j++) {
                const tile = document.createElement('div');
                tile.className = 'tile';
                tile.id = `tile-${i}-${j}`;
                row.appendChild(tile);
            }

            gameBoard.appendChild(row);
        }
    }

    setupEventListeners() {
        // Physical keyboard
        document.addEventListener('keydown', (e) => this.handleKeyPress(e.key.toUpperCase()));

        // On-screen keyboard
        document.querySelectorAll('.key').forEach(key => {
            key.addEventListener('click', () => {
                this.handleKeyPress(key.dataset.key);
            });
        });

        // Control buttons
        document.getElementById('mode-toggle').addEventListener('click', () => this.toggleMode());
        document.getElementById('restart-btn').addEventListener('click', () => this.newGame());
        document.getElementById('stats-btn').addEventListener('click', () => this.showStats());
        document.getElementById('close-stats').addEventListener('click', () => this.hideStats());
        document.getElementById('close-fact').addEventListener('click', () => this.hideFact());

        // Modal close on background click
        document.getElementById('stats-modal').addEventListener('click', (e) => {
            if (e.target.id === 'stats-modal') this.hideStats();
        });
    }

    newGame() {
        // Reset game state
        this.gameState = {
            currentRow: 0,
            currentCol: 0,
            gameOver: false,
            won: false,
            targetWord: this.getRandomWord(),
            guesses: [],
            atoMode: this.gameState.atoMode
        };

        // Clear board
        document.querySelectorAll('.tile').forEach(tile => {
            tile.textContent = '';
            tile.className = 'tile';
        });

        // Reset keyboard
        document.querySelectorAll('.key').forEach(key => {
            key.className = key.className.replace(/\s*(correct|present|absent)/g, '');
        });

        this.hideMessage();
        this.hideFact();
        this.saveGameState();

        console.log('New game started. Target word:', this.gameState.targetWord);
    }

    getRandomWord() {
        const wordList = this.gameState.atoMode ? this.atoWords : this.englishWords;
        return wordList[Math.floor(Math.random() * wordList.length)];
    }

    toggleMode() {
        this.gameState.atoMode = !this.gameState.atoMode;
        const modeBtn = document.getElementById('mode-toggle');
        modeBtn.textContent = this.gameState.atoMode ? 'ATO Mode' : 'Normal Mode';
        this.newGame();
    }

    handleKeyPress(key) {
        if (this.gameState.gameOver) return;

        if (key === 'ENTER') {
            this.submitGuess();
        } else if (key === 'BACKSPACE') {
            this.deleteLetter();
        } else if (key.match(/^[A-Z]$/) && this.gameState.currentCol < 5) {
            this.addLetter(key);
        }
    }

    addLetter(letter) {
        if (this.gameState.currentCol < 5) {
            const tile = document.getElementById(`tile-${this.gameState.currentRow}-${this.gameState.currentCol}`);
            tile.textContent = letter;
            tile.classList.add('filled');
            this.gameState.currentCol++;
        }
    }

    deleteLetter() {
        if (this.gameState.currentCol > 0) {
            this.gameState.currentCol--;
            const tile = document.getElementById(`tile-${this.gameState.currentRow}-${this.gameState.currentCol}`);
            tile.textContent = '';
            tile.classList.remove('filled');
        }
    }

    submitGuess() {
        if (this.gameState.currentCol !== 5) {
            this.showMessage('Not enough letters');
            return;
        }

        const guess = this.getCurrentGuess();
        
        // Validate word (in a real implementation, you'd check against a dictionary)
        if (!this.isValidWord(guess)) {
            this.showMessage('Not a valid word');
            return;
        }

        this.gameState.guesses.push(guess);
        this.evaluateGuess(guess);
        this.updateKeyboard(guess);

        if (guess === this.gameState.targetWord) {
            this.gameState.won = true;
            this.gameState.gameOver = true;
            this.showMessage('Congratulations!');
            this.updateStats(true);
            if (this.gameState.atoMode) {
                setTimeout(() => this.showRandomFact(), 1500);
            }
        } else if (this.gameState.currentRow === 5) {
            this.gameState.gameOver = true;
            this.showMessage(`Game Over! The word was ${this.gameState.targetWord}`);
            this.updateStats(false);
        } else {
            this.gameState.currentRow++;
            this.gameState.currentCol = 0;
        }

        this.saveGameState();
    }

    getCurrentGuess() {
        let guess = '';
        for (let i = 0; i < 5; i++) {
            const tile = document.getElementById(`tile-${this.gameState.currentRow}-${i}`);
            guess += tile.textContent;
        }
        return guess;
    }

    isValidWord(word) {
        // Check if word is in our word lists or common English words
        const allWords = [...this.atoWords, ...this.englishWords];
        const commonWords = [
            'ABOUT', 'ABOVE', 'ABUSE', 'ACTOR', 'ACUTE', 'ADMIT', 'ADOPT', 'ADULT',
            'AFTER', 'AGAIN', 'AGENT', 'AGREE', 'AHEAD', 'ALARM', 'ALBUM', 'ALERT',
            'ALIEN', 'ALIGN', 'ALIKE', 'ALIVE', 'ALLOW', 'ALONE', 'ALONG', 'ALTER',
            'AMONG', 'ANGER', 'ANGLE', 'ANGRY', 'APART', 'APPLE', 'APPLY', 'ARENA',
            'ARGUE', 'ARISE', 'ARRAY', 'ASIDE', 'ASSET', 'AVOID', 'AWAKE', 'AWARD',
            'AWARE', 'BADLY', 'BAKER', 'BASES', 'BASIC', 'BEACH', 'BEGAN', 'BEGIN',
            'BEING', 'BELOW', 'BENCH', 'BILLY', 'BIRTH', 'BLACK', 'BLAME', 'BLANK',
            'BLIND', 'BLOCK', 'BLOOD', 'BOARD', 'BOOST', 'BOOTH', 'BOUND', 'BRAIN',
            'BRAND', 'BRASS', 'BRAVE', 'BREAD', 'BREAK', 'BREED', 'BRIEF', 'BRING',
            'BROAD', 'BROKE', 'BROWN', 'BUILD', 'BUILT', 'BUYER', 'CABLE', 'CALIF',
            'CARRY', 'CATCH', 'CAUSE', 'CHAIN', 'CHAIR', 'CHAOS', 'CHARM', 'CHART',
            'CHASE', 'CHEAP', 'CHECK', 'CHEST', 'CHIEF', 'CHILD', 'CHINA', 'CHOSE',
            'CIVIL', 'CLAIM', 'CLASS', 'CLEAN', 'CLEAR', 'CLICK', 'CLIMB', 'CLOCK',
            'CLOSE', 'CLOUD', 'COACH', 'COAST', 'COULD', 'COUNT', 'COURT', 'COVER',
            'CRAFT', 'CRASH', 'CRAZY', 'CREAM', 'CRIME', 'CROSS', 'CROWD', 'CROWN',
            'CRUDE', 'CURVE', 'CYCLE', 'DAILY', 'DANCE', 'DATED', 'DEALT', 'DEATH',
            'DEBUT', 'DELAY', 'DEPTH', 'DOING', 'DOUBT', 'DOZEN', 'DRAFT', 'DRAMA',
            'DRANK', 'DRAWN', 'DREAM', 'DRESS', 'DRILL', 'DRINK', 'DRIVE', 'DROVE',
            'DYING', 'EAGER', 'EARLY', 'EARTH', 'EIGHT', 'ELITE', 'EMPTY', 'ENEMY',
            'ENJOY', 'ENTER', 'ENTRY', 'EQUAL', 'ERROR', 'EVENT', 'EVERY', 'EXACT',
            'EXIST', 'EXTRA', 'FAITH', 'FALSE', 'FAULT', 'FIBER', 'FIELD', 'FIFTH',
            'FIFTY', 'FIGHT', 'FINAL', 'FIRST', 'FIXED', 'FLASH', 'FLEET', 'FLOOR',
            'FLUID', 'FOCUS', 'FORCE', 'FORTH', 'FORTY', 'FORUM', 'FOUND', 'FRAME',
            'FRANK', 'FRAUD', 'FRESH', 'FRONT', 'FRUIT', 'FULLY', 'FUNNY', 'GIANT',
            'GIVEN', 'GLASS', 'GLOBE', 'GOING', 'GRACE', 'GRADE', 'GRAND', 'GRANT',
            'GRASS', 'GRAVE', 'GREAT', 'GREEN', 'GROSS', 'GROUP', 'GROWN', 'GUARD',
            'GUESS', 'GUEST', 'GUIDE', 'HAPPY', 'HARRY', 'HEART', 'HEAVY', 'HENCE',
            'HENRY', 'HORSE', 'HOTEL', 'HOUSE', 'HUMAN', 'HURRY', 'IMAGE', 'INDEX',
            'INNER', 'INPUT', 'ISSUE', 'JAPAN', 'JIMMY', 'JOINT', 'JONES', 'JUDGE',
            'KNOWN', 'LABEL', 'LARGE', 'LASER', 'LATER', 'LAUGH', 'LAYER', 'LEARN',
            'LEASE', 'LEAST', 'LEAVE', 'LEGAL', 'LEVEL', 'LEWIS', 'LIGHT', 'LIMIT',
            'LINKS', 'LIVES', 'LOCAL', 'LOOSE', 'LOWER', 'LUCKY', 'LUNCH', 'LYING',
            'MAGIC', 'MAJOR', 'MAKER', 'MARCH', 'MARIA', 'MATCH', 'MAYBE', 'MAYOR',
            'MEANT', 'MEDIA', 'METAL', 'MIGHT', 'MINOR', 'MINUS', 'MIXED', 'MODEL',
            'MONEY', 'MONTH', 'MORAL', 'MOTOR', 'MOUNT', 'MOUSE', 'MOUTH', 'MOVED',
            'MOVIE', 'MUSIC', 'NEEDS', 'NEVER', 'NEWLY', 'NIGHT', 'NOISE', 'NORTH',
            'NOTED', 'NOVEL', 'NURSE', 'OCCUR', 'OCEAN', 'OFFER', 'OFTEN', 'ORDER',
            'OTHER', 'OUGHT', 'PAINT', 'PANEL', 'PAPER', 'PARTY', 'PEACE', 'PETER',
            'PHASE', 'PHONE', 'PHOTO', 'PIANO', 'PIECE', 'PILOT', 'PITCH', 'PLACE',
            'PLAIN', 'PLANE', 'PLANT', 'PLATE', 'POINT', 'POUND', 'POWER', 'PRESS',
            'PRICE', 'PRIDE', 'PRIME', 'PRINT', 'PRIOR', 'PRIZE', 'PROOF', 'PROUD',
            'PROVE', 'QUEEN', 'QUICK', 'QUIET', 'QUITE', 'RADIO', 'RAISE', 'RANGE',
            'RAPID', 'RATIO', 'REACH', 'READY', 'REALM', 'REBEL', 'REFER', 'RELAX',
            'REPAY', 'REPLY', 'RIGHT', 'RIGID', 'RIVAL', 'RIVER', 'ROBIN', 'ROGER',
            'ROMAN', 'ROUGH', 'ROUND', 'ROUTE', 'ROYAL', 'RURAL', 'SCALE', 'SCENE',
            'SCOPE', 'SCORE', 'SENSE', 'SERVE', 'SEVEN', 'SHALL', 'SHAPE', 'SHARE',
            'SHARP', 'SHEET', 'SHELF', 'SHELL', 'SHIFT', 'SHINE', 'SHIRT', 'SHOCK',
            'SHOOT', 'SHORT', 'SHOWN', 'SIGHT', 'SILLY', 'SINCE', 'SIXTH', 'SIXTY',
            'SIZED', 'SKILL', 'SLEEP', 'SLIDE', 'SMALL', 'SMART', 'SMILE', 'SMITH',
            'SMOKE', 'SNAKE', 'SNOW', 'SOLID', 'SOLVE', 'SORRY', 'SOUND', 'SOUTH',
            'SPACE', 'SPARE', 'SPEAK', 'SPEED', 'SPEND', 'SPENT', 'SPLIT', 'SPOKE',
            'SPORT', 'STAFF', 'STAGE', 'STAKE', 'STAND', 'START', 'STATE', 'STEAM',
            'STEEL', 'STEEP', 'STEER', 'STICK', 'STILL', 'STOCK', 'STONE', 'STOOD',
            'STORE', 'STORM', 'STORY', 'STRIP', 'STUCK', 'STUDY', 'STUFF', 'STYLE',
            'SUGAR', 'SUITE', 'SUPER', 'SWEET', 'TABLE', 'TAKEN', 'TASTE', 'TAXES',
            'TEACH', 'TEAM', 'TEETH', 'TERRY', 'TEXAS', 'THANK', 'THEFT', 'THEIR',
            'THEME', 'THERE', 'THESE', 'THICK', 'THING', 'THINK', 'THIRD', 'THOSE',
            'THREE', 'THREW', 'THROW', 'THUMB', 'TIGER', 'TIGHT', 'TIMER', 'TIRED',
            'TITLE', 'TODAY', 'TOKEN', 'TOPIC', 'TOTAL', 'TOUCH', 'TOUGH', 'TOWER',
            'TRACK', 'TRADE', 'TRAIN', 'TREAT', 'TREND', 'TRIAL', 'TRIBE', 'TRICK',
            'TRIED', 'TRIES', 'TRIP', 'TRUCK', 'TRULY', 'TRUNK', 'TRUST', 'TRUTH',
            'TWICE', 'UNCLE', 'UNDER', 'UNDUE', 'UNION', 'UNITY', 'UNTIL', 'UPPER',
            'UPSET', 'URBAN', 'USAGE', 'USUAL', 'VALID', 'VALUE', 'VIDEO', 'VIRUS',
            'VISIT', 'VITAL', 'VOCAL', 'VOICE', 'WASTE', 'WATCH', 'WATER', 'WHEEL',
            'WHERE', 'WHICH', 'WHILE', 'WHITE', 'WHOLE', 'WHOSE', 'WOMAN', 'WOMEN',
            'WORLD', 'WORRY', 'WORSE', 'WORST', 'WORTH', 'WOULD', 'WRITE', 'WRONG',
            'WROTE', 'YOUNG', 'YOUTH'
        ];

        return word.length === 5 &&
               word.match(/^[A-Z]+$/) &&
               (allWords.includes(word) || commonWords.includes(word));
    }

    evaluateGuess(guess) {
        const targetWord = this.gameState.targetWord;
        const result = [];

        // First pass: mark correct letters
        for (let i = 0; i < 5; i++) {
            if (guess[i] === targetWord[i]) {
                result[i] = 'correct';
            }
        }

        // Second pass: mark present letters
        const targetLetterCount = {};
        for (let i = 0; i < 5; i++) {
            if (result[i] !== 'correct') {
                targetLetterCount[targetWord[i]] = (targetLetterCount[targetWord[i]] || 0) + 1;
            }
        }

        for (let i = 0; i < 5; i++) {
            if (result[i] !== 'correct') {
                if (targetLetterCount[guess[i]] > 0) {
                    result[i] = 'present';
                    targetLetterCount[guess[i]]--;
                } else {
                    result[i] = 'absent';
                }
            }
        }

        // Apply results to tiles with flip animation
        for (let i = 0; i < 5; i++) {
            const tile = document.getElementById(`tile-${this.gameState.currentRow}-${i}`);
            setTimeout(() => {
                tile.classList.add('flip');
                setTimeout(() => {
                    tile.classList.add(result[i]);
                    tile.classList.remove('flip');
                }, 300);
            }, i * 100);
        }

        return result;
    }

    updateKeyboard(guess) {
        const targetWord = this.gameState.targetWord;
        
        for (let i = 0; i < guess.length; i++) {
            const letter = guess[i];
            const key = document.querySelector(`[data-key="${letter}"]`);
            
            if (!key) continue;

            let status = 'absent';
            if (targetWord.includes(letter)) {
                status = 'present';
                if (targetWord[i] === letter) {
                    status = 'correct';
                }
            }

            // Only update if the new status is "better" than the current one
            if (!key.classList.contains('correct')) {
                if (status === 'correct' || (status === 'present' && !key.classList.contains('present'))) {
                    key.className = key.className.replace(/\s*(correct|present|absent)/g, '');
                    key.classList.add(status);
                } else if (status === 'absent' && !key.classList.contains('present')) {
                    key.classList.add('absent');
                }
            }
        }
    }

    showMessage(text) {
        const message = document.getElementById('message');
        message.textContent = text;
        message.classList.remove('hidden');
        setTimeout(() => this.hideMessage(), 2000);
    }

    hideMessage() {
        document.getElementById('message').classList.add('hidden');
    }

    showRandomFact() {
        const fact = this.atoFacts[Math.floor(Math.random() * this.atoFacts.length)];
        document.getElementById('fact-text').textContent = fact;
        document.getElementById('did-you-know').classList.remove('hidden');
    }

    hideFact() {
        document.getElementById('did-you-know').classList.add('hidden');
    }

    showStats() {
        const stats = this.getStats();
        document.getElementById('games-played').textContent = stats.gamesPlayed;
        document.getElementById('win-percentage').textContent = stats.winPercentage;
        document.getElementById('current-streak').textContent = stats.currentStreak;
        document.getElementById('max-streak').textContent = stats.maxStreak;
        
        this.updateDistributionChart(stats.guessDistribution);
        document.getElementById('stats-modal').classList.remove('hidden');
    }

    hideStats() {
        document.getElementById('stats-modal').classList.add('hidden');
    }

    updateDistributionChart(distribution) {
        const chart = document.getElementById('distribution-chart');
        chart.innerHTML = '';
        
        for (let i = 1; i <= 6; i++) {
            const row = document.createElement('div');
            row.style.display = 'flex';
            row.style.alignItems = 'center';
            row.style.marginBottom = '4px';
            
            const label = document.createElement('div');
            label.textContent = i;
            label.style.width = '20px';
            label.style.textAlign = 'center';
            
            const bar = document.createElement('div');
            bar.style.backgroundColor = 'var(--ato-blue)';
            bar.style.height = '20px';
            bar.style.marginLeft = '10px';
            bar.style.minWidth = '20px';
            bar.style.width = `${(distribution[i] || 0) * 20}px`;
            bar.style.color = 'white';
            bar.style.textAlign = 'center';
            bar.style.lineHeight = '20px';
            bar.style.fontSize = '12px';
            bar.textContent = distribution[i] || 0;
            
            row.appendChild(label);
            row.appendChild(bar);
            chart.appendChild(row);
        }
    }

    getStats() {
        const stats = JSON.parse(localStorage.getItem('ato-wordle-stats') || '{}');
        return {
            gamesPlayed: stats.gamesPlayed || 0,
            gamesWon: stats.gamesWon || 0,
            winPercentage: stats.gamesPlayed ? Math.round((stats.gamesWon / stats.gamesPlayed) * 100) : 0,
            currentStreak: stats.currentStreak || 0,
            maxStreak: stats.maxStreak || 0,
            guessDistribution: stats.guessDistribution || {}
        };
    }

    updateStats(won) {
        const stats = this.getStats();
        stats.gamesPlayed++;
        
        if (won) {
            stats.gamesWon++;
            stats.currentStreak++;
            stats.maxStreak = Math.max(stats.maxStreak, stats.currentStreak);
            
            const guessCount = this.gameState.currentRow + 1;
            stats.guessDistribution[guessCount] = (stats.guessDistribution[guessCount] || 0) + 1;
        } else {
            stats.currentStreak = 0;
        }
        
        localStorage.setItem('ato-wordle-stats', JSON.stringify(stats));
    }

    saveGameState() {
        localStorage.setItem('ato-wordle-game', JSON.stringify(this.gameState));
    }

    loadGameState() {
        const saved = localStorage.getItem('ato-wordle-game');
        if (saved) {
            const savedState = JSON.parse(saved);
            // Only load if it's from today (for daily word feature)
            this.gameState.atoMode = savedState.atoMode !== undefined ? savedState.atoMode : true;
        }
        
        // Update mode button
        const modeBtn = document.getElementById('mode-toggle');
        modeBtn.textContent = this.gameState.atoMode ? 'ATO Mode' : 'Normal Mode';
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ATOWordle();
});
