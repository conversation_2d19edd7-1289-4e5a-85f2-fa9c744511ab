<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATO Wordle - Tax Office Word Game</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1 class="title">ATO WORDLE</h1>
            <p class="subtitle">Australian Taxation Office Word Game</p>
            <div class="controls">
                <button id="mode-toggle" class="btn">ATO Mode</button>
                <button id="restart-btn" class="btn">New Game</button>
                <button id="stats-btn" class="btn">Stats</button>
            </div>
        </header>

        <!-- Game Board -->
        <main class="game-container">
            <div id="game-board" class="game-board">
                <!-- Grid will be generated by JavaScript -->
            </div>

            <!-- On-screen Keyboard -->
            <div id="keyboard" class="keyboard">
                <div class="keyboard-row">
                    <button class="key" data-key="Q">Q</button>
                    <button class="key" data-key="W">W</button>
                    <button class="key" data-key="E">E</button>
                    <button class="key" data-key="R">R</button>
                    <button class="key" data-key="T">T</button>
                    <button class="key" data-key="Y">Y</button>
                    <button class="key" data-key="U">U</button>
                    <button class="key" data-key="I">I</button>
                    <button class="key" data-key="O">O</button>
                    <button class="key" data-key="P">P</button>
                </div>
                <div class="keyboard-row">
                    <button class="key" data-key="A">A</button>
                    <button class="key" data-key="S">S</button>
                    <button class="key" data-key="D">D</button>
                    <button class="key" data-key="F">F</button>
                    <button class="key" data-key="G">G</button>
                    <button class="key" data-key="H">H</button>
                    <button class="key" data-key="J">J</button>
                    <button class="key" data-key="K">K</button>
                    <button class="key" data-key="L">L</button>
                </div>
                <div class="keyboard-row">
                    <button class="key key-large" data-key="ENTER">ENTER</button>
                    <button class="key" data-key="Z">Z</button>
                    <button class="key" data-key="X">X</button>
                    <button class="key" data-key="C">C</button>
                    <button class="key" data-key="V">V</button>
                    <button class="key" data-key="B">B</button>
                    <button class="key" data-key="N">N</button>
                    <button class="key" data-key="M">M</button>
                    <button class="key key-large" data-key="BACKSPACE">⌫</button>
                </div>
            </div>
        </main>

        <!-- Game Messages -->
        <div id="message" class="message hidden"></div>

        <!-- Did You Know Box -->
        <div id="did-you-know" class="did-you-know hidden">
            <h3>Did You Know?</h3>
            <p id="fact-text"></p>
            <button id="close-fact" class="btn">Close</button>
        </div>

        <!-- Statistics Modal -->
        <div id="stats-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Statistics</h2>
                    <button id="close-stats" class="close-btn">&times;</button>
                </div>
                <div class="stats-grid">
                    <div class="stat">
                        <div class="stat-number" id="games-played">0</div>
                        <div class="stat-label">Played</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="win-percentage">0</div>
                        <div class="stat-label">Win %</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="current-streak">0</div>
                        <div class="stat-label">Current Streak</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="max-streak">0</div>
                        <div class="stat-label">Max Streak</div>
                    </div>
                </div>
                <div class="guess-distribution">
                    <h3>Guess Distribution</h3>
                    <div id="distribution-chart"></div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="instructions">
            <h3>How to Play</h3>
            <p>Guess the ATO-related word in 6 tries. Each guess must be a valid word.</p>
            <div class="example-tiles">
                <div class="tile correct">T</div>
                <div class="tile present">A</div>
                <div class="tile absent">X</div>
            </div>
            <p><strong>Green:</strong> Correct letter, correct position<br>
               <strong>Yellow:</strong> Correct letter, wrong position<br>
               <strong>Gray:</strong> Letter not in word</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
